<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">

<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:<EMAIL>" />
<link rel="icon" type="image/png" href="/graphics/gnu-head-mini.png" />
<meta name="ICBM" content="42.256233,-71.006581" />
<meta name="DC.title" content="gnu.org" />



<title>GNU Lesser General Public License - GNU Project - Free Software Foundation (FSF)</title>
<link rel="alternate" type="application/rdf+xml" href="lgpl-3.0.rdf" /> 

<!-- start of banner.html -->
<!-- start of head-include-2.html -->

<style type="text/css" media="screen">
@import url('/style.css');
</style>

<style type="text/css" media="handheld">
@import url('/mini.css');
</style>

<!--[if IE]>
<link rel="stylesheet" href="/style.css" media="screen" type="text/css" />
<![endif]-->

<style type="text/css" media="print">
@import url('/print.css');
</style>
<meta name="viewport" content="width=320; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;" />
<!-- end of head-include-2.html -->

</head>
<body>
<div id="toplinks"><span class="netscape4"><a href="#content">Skip to content</a> | <a href="#navigation">Skip to navigation</a> | <a href="#searcher">Skip to search</a>| </span><a href="#translations">Translations of this page</a> | <a href="/accessibility/">Accessibility</a></div>


<div id="null-wrapper">

      <div id="header">
        <div class="inner" style="position: relative;">

        <h1 id="logo"><a href="/">The GNU Operating System</a></h1>

	  <div style="position: absolute; top: -20px; right: 0; width: 344px;"><a href="http://patentabsurdity.com/watch.html" title="Watch Patent Absurdity"><img src="http://static.fsf.org/nosvn/pa-gnu.jpg" alt="" /></a>
	  </div>

<!--	  	<div id="fssbox">
	  <h4>Sign up for the <em>Free Software Supporter</em></h4>
	  <p>A monthly update on GNU and the FSF</p>
	  <form action="http://lists.gnu.org/mailman/subscribe/info-fsf" method="post">
	  <p><input type="text" id="frmEmail" name="email" size="15" maxlength="80" value="<EMAIL>" onfocus="this.value=''" /> &nbsp; <input type="submit" value="Ok" /></p>
	  </form>
	</div>

-->
	
	</div>

      </div>

      <div id="navigation">
        <div class="inner">
        <ul>
          <li id="tabPhilosophy"><a href="/philosophy/philosophy.html">Philosophy</a></li>
          <li id="tabLicenses"><a href="/licenses/licenses.html">Licenses</a></li>	  
          <li id="tabSoftware"><a href="/software/software.html">Downloads</a></li>
          <li id="tabDoc"><a href="/doc/doc.html">Documentation</a></li>
          <li><a href="/help/help.html">Help&nbsp;GNU</a></li>
          <li id="joinfsftab"><a href="https://www.fsf.org/associate/support_freedom?referrer=4052">Join the FSF!</a></li>
        </ul>

	    	  	  <div id="searcher">

  <form method="get" action="http://www.gnu.org/cgi-bin/estseek.cgi">
   <div><label class="netscape4" for="phrase">Search:</label>
   <input name="phrase" id="phrase" type="text" size="18" accesskey="s" value="Why GNU/Linux?" />
   <input type="submit" value="Search" /></div>
                                </form>
                        </div>

        </div>
      </div>


<div id="content" class="inner">
<!-- end of banner.html -->


<h2>GNU Lesser General Public License</h2>

<img src="/graphics/lgplv3-147x51.png" alt="" style="float: right;" />

<ul>
  <li><a href="/licenses/why-not-lgpl.html">Why you shouldn't use the
       Lesser GPL for your next library</a></li>
  <li><a href="/licenses/gpl-faq.html">Frequently Asked Questions about
       the GNU licenses</a></li>
  <li><a href="/licenses/gpl-howto.html">How to use GNU licenses for your
       own software</a></li>
  <li><a href="/licenses/translations.html">Translations
       of the LGPL</a></li>
  <li>The GNU LGPL in other formats: 
       <a href="/licenses/lgpl-3.0.txt">plain text</a>,
       <a href="/licenses/lgpl-3.0.dbk">Docbook</a>,
       <a href="/licenses/lgpl-3.0-standalone.html">standalone HTML</a>,
       <a href="/licenses/lgpl-3.0.tex">LaTeX</a>,
       <a href="/licenses/lgpl-3.0.texi">Texinfo</a></li>
  <li><a href="/graphics/license-logos.html">LGPLv3 logos</a> to use
       with your project</li>
  <li><a href="/licenses/old-licenses/old-licenses.html#LGPL">Old
       versions of the GNU LGPL</a></li>
  <li><a href="/licenses/gpl-violation.html">What to do if you see a
       possible LGPL violation</a></li>
</ul>

<p>This license is a set of additional permissions added to <a
href="/licenses/gpl-3.0.html">version 3 of the GNU General Public
License</a>.  For more information about how to release your own software
under this license, please see our <a href="/licenses/gpl-howto.html">page
of instructions</a>.</p>

<hr style="clear: both;" />

<!-- The license text is in English and appears broken in RTL as
     Arabic, Farsi, etc.  Explicitly set the direction to override the
     one defined in the translation. -->
<div dir="ltr">
<h3 style="text-align: center;">GNU LESSER GENERAL PUBLIC LICENSE</h3>
<p style="text-align: center;">Version 3, 29 June 2007</p>

<p>Copyright &copy; 2007 Free Software Foundation, Inc.
 &lt;<a href="http://fsf.org/">http://fsf.org/</a>&gt;</p><p>
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.</p>

<p>This version of the GNU Lesser General Public License incorporates
the terms and conditions of version 3 of the GNU General Public
License, supplemented by the additional permissions listed below.</p>

<h4><a name="section0"></a>0. Additional Definitions.</h4>

<p>As used herein, &ldquo;this License&rdquo; refers to version 3 of the GNU Lesser
General Public License, and the &ldquo;GNU GPL&rdquo; refers to version 3 of the GNU
General Public License.</p>

<p>&ldquo;The Library&rdquo; refers to a covered work governed by this License,
other than an Application or a Combined Work as defined below.</p>

<p>An &ldquo;Application&rdquo; is any work that makes use of an interface provided
by the Library, but which is not otherwise based on the Library.
Defining a subclass of a class defined by the Library is deemed a mode
of using an interface provided by the Library.</p>

<p>A &ldquo;Combined Work&rdquo; is a work produced by combining or linking an
Application with the Library.  The particular version of the Library
with which the Combined Work was made is also called the &ldquo;Linked
Version&rdquo;.</p>

<p>The &ldquo;Minimal Corresponding Source&rdquo; for a Combined Work means the
Corresponding Source for the Combined Work, excluding any source code
for portions of the Combined Work that, considered in isolation, are
based on the Application, and not on the Linked Version.</p>

<p>The &ldquo;Corresponding Application Code&rdquo; for a Combined Work means the
object code and/or source code for the Application, including any data
and utility programs needed for reproducing the Combined Work from the
Application, but excluding the System Libraries of the Combined Work.</p>

<h4><a name="section1"></a>1. Exception to Section 3 of the GNU GPL.</h4>

<p>You may convey a covered work under sections 3 and 4 of this License
without being bound by section 3 of the GNU GPL.</p>

<h4><a name="section2"></a>2. Conveying Modified Versions.</h4>

<p>If you modify a copy of the Library, and, in your modifications, a
facility refers to a function or data to be supplied by an Application
that uses the facility (other than as an argument passed when the
facility is invoked), then you may convey a copy of the modified
version:</p>

<ul>
<li>a) under this License, provided that you make a good faith effort to
   ensure that, in the event an Application does not supply the
   function or data, the facility still operates, and performs
   whatever part of its purpose remains meaningful, or</li>

<li>b) under the GNU GPL, with none of the additional permissions of
   this License applicable to that copy.</li>
</ul>

<h4><a name="section3"></a>3. Object Code Incorporating Material from Library Header Files.</h4>

<p>The object code form of an Application may incorporate material from
a header file that is part of the Library.  You may convey such object
code under terms of your choice, provided that, if the incorporated
material is not limited to numerical parameters, data structure
layouts and accessors, or small macros, inline functions and templates
(ten or fewer lines in length), you do both of the following:</p>

<ul>
<li>a) Give prominent notice with each copy of the object code that the
   Library is used in it and that the Library and its use are
   covered by this License.</li>

<li>b) Accompany the object code with a copy of the GNU GPL and this license
   document.</li>
</ul>

<h4><a name="section4"></a>4. Combined Works.</h4>

<p>You may convey a Combined Work under terms of your choice that,
taken together, effectively do not restrict modification of the
portions of the Library contained in the Combined Work and reverse
engineering for debugging such modifications, if you also do each of
the following:</p>

<ul>
<li>a) Give prominent notice with each copy of the Combined Work that
   the Library is used in it and that the Library and its use are
   covered by this License.</li>

<li>b) Accompany the Combined Work with a copy of the GNU GPL and this license
   document.</li>

<li>c) For a Combined Work that displays copyright notices during
   execution, include the copyright notice for the Library among
   these notices, as well as a reference directing the user to the
   copies of the GNU GPL and this license document.</li>

<li>d) Do one of the following:

<ul>
<li>0) Convey the Minimal Corresponding Source under the terms of this
       License, and the Corresponding Application Code in a form
       suitable for, and under terms that permit, the user to
       recombine or relink the Application with a modified version of
       the Linked Version to produce a modified Combined Work, in the
       manner specified by section 6 of the GNU GPL for conveying
       Corresponding Source.</li>

<li>1) Use a suitable shared library mechanism for linking with the
       Library.  A suitable mechanism is one that (a) uses at run time
       a copy of the Library already present on the user's computer
       system, and (b) will operate properly with a modified version
       of the Library that is interface-compatible with the Linked
       Version.</li>
</ul></li>

<li>e) Provide Installation Information, but only if you would otherwise
   be required to provide such information under section 6 of the
   GNU GPL, and only to the extent that such information is
   necessary to install and execute a modified version of the
   Combined Work produced by recombining or relinking the
   Application with a modified version of the Linked Version. (If
   you use option 4d0, the Installation Information must accompany
   the Minimal Corresponding Source and Corresponding Application
   Code. If you use option 4d1, you must provide the Installation
   Information in the manner specified by section 6 of the GNU GPL
   for conveying Corresponding Source.)</li>
</ul>

<h4><a name="section5"></a>5. Combined Libraries.</h4>

<p>You may place library facilities that are a work based on the
Library side by side in a single library together with other library
facilities that are not Applications and are not covered by this
License, and convey such a combined library under terms of your
choice, if you do both of the following:</p>

<ul>
<li>a) Accompany the combined library with a copy of the same work based
   on the Library, uncombined with any other library facilities,
   conveyed under the terms of this License.</li>

<li>b) Give prominent notice with the combined library that part of it
   is a work based on the Library, and explaining where to find the
   accompanying uncombined form of the same work.</li>
</ul>

<h4><a name="section6"></a>6. Revised Versions of the GNU Lesser General Public License.</h4>

<p>The Free Software Foundation may publish revised and/or new versions
of the GNU Lesser General Public License from time to time. Such new
versions will be similar in spirit to the present version, but may
differ in detail to address new problems or concerns.</p>

<p>Each version is given a distinguishing version number. If the
Library as you received it specifies that a certain numbered version
of the GNU Lesser General Public License &ldquo;or any later version&rdquo;
applies to it, you have the option of following the terms and
conditions either of that published version or of any later version
published by the Free Software Foundation. If the Library as you
received it does not specify a version number of the GNU Lesser
General Public License, you may choose any version of the GNU Lesser
General Public License ever published by the Free Software Foundation.</p>

<p>If the Library as you received it specifies that a proxy can decide
whether future versions of the GNU Lesser General Public License shall
apply, that proxy's public statement of acceptance of any version is
permanent authorization for you to choose that version for the
Library.</p>

</div>
</div>

              
<div class="inner">
    
<div class="yui-g" id="fsf-links">
    <div class="yui-g first">

        <div class="yui-u first" id="sitemap-1">

          <p class="netscape4"><a href="#footer">Skip sitemap</a> or <a href="#sitemap-2">skip to licensing items</a></p>

      <ul>
	<li><a href="/gnu/gnu-history.html">GNU History</a></li>
        <li><a href="/help/">Get involved</a>

        <ul>

          <li><a href="http://savannah.gnu.org/people/?type_id=1">Projects that need help</a></li>
          <li><a href="/server/standards/translations/priorities.html#Languages">Help translate this website</a></li>
        </ul></li>
        <li><a href="/server/takeaction.html#unmaint">Take over an unmaintained package</a></li>
        <li><a href="/server/takeaction.html#gnustep">Use GNUstep</a></li>
	  <li><a href="/distros/free-distros.html">Download GNU</a></li>
	  <li><a href="http://directory.fsf.org/GNU/">GNU packages</a></li>
	  <li><a href="/manual/manual.html">Free documentation</a></li>
	  <li><a href="http://lists.gnu.org/">GNU mailing lists</a></li>
	  <li><a href="http://savannah.gnu.org/">GNU savannah</a></li>
        <li><a href="http://libreplanet.org/">Connect with free software users</a></li>
	<li><a href="/people/">GNU's Who?</a></li>
	<li><a href="http://planet.gnu.org/">Planet GNU</a></li>
	<li><a href="/contact/gnu-advisory.html">GNU Advisory Committee</a></li>
      </ul>

    </div>

    <div class="yui-u" id="sitemap-2">

          <p class="netscape4"><a href="#sitemap-3">Skip to general items</a></p>

       <ul>
        <li><a href="http://www.fsf.org/licensing">Software licensing</a>
        <ul>
        <li><a href="http://www.fsf.org/licensing/education">Licensing education</a></li>
        <li><a href="/licenses">Free software licenses</a>
        <ul>
        <li><a href="/licenses/gpl.html">GNU GPL</a></li>

        <li><a href="/licenses/agpl.html">GNU AGPL</a></li>
        <li><a href="/licenses/lgpl.html">GNU LGPL</a></li>
        <li><a href="/licenses/fdl.html">GNU FDL</a></li></ul></li>
        <li><a href="/licenses/gpl-faq.html">Software licensing FAQ</a></li>
        <li><a href="/licenses/gpl-faq.html">Licensing compliance</a></li>
        <li><a href="/licenses/gpl-faq.html">How to use GNU licenses for your own software</a></li></ul></li>
	<li><a href="http://www.fsf.org/resources/service/">GNU Service Directory</a></li>
	<li><a href="/fun/fun.html">GNU Fun</a></li>
	<li><a href="/graphics/graphics.html">GNU Art</a></li>
	<li><a href="/music/music.html">Music &amp; Songs</a></li>

      </ul>

    
    </div>
    </div>
    <div class="yui-g first">
        <div class="yui-u first" id="sitemap-3">

          <p class="netscape4"><a href="#sitemap-4">Skip to philosophical items</a></p>
    
      <ul>

      <li><a href="http://www.fsf.org/news/">Latest News</a><ul>
        <li><a href="http://www.fsf.org/events/">Upcoming Events</a></li>
      <li><a href="http://www.fsf.org/blogs/">FSF Blogs</a></li></ul></li>
      <li><a href="http://www.fsf.org/volunteer">Volunteering and internships</a></li>
      <li><a href="http://www.fsf.org/resources/hw">Hardware Database</a></li>
      <li><a href="http://directory.fsf.org">Free Software Directory</a></li>

        <li><a href="http://www.fsf.org/resources/">Free Software Resources</a></li>
      <li><a href="http://www.fsf.org/associate/">Associate Members</a></li>
      <li><a href="http://www.fsf.org/associate/account/">My FSF Account</a></li>
      <li class="noright"><a href="http://www.fsf.org/about/contact.html">Contact the FSF</a></li>
      <li><a href="http://audio-video.gnu.org/">GNU Audio/Video</a></li>
<li><a href="/accessibility/accessibility.html">Accessibility Statement</a></li>
<li><a href="http://ftp.gnu.org/gnu/">GNU FTP Site</a>
    and <a href="/prep/ftp.html">mirrors</a></li>
<li><a href="/people/speakers.html">GNU Speakers</a></li>
<li><a href="http://www.fsf.org/jobs">Free software jobs</a></li>
      </ul>

      
    </div>
        <div class="yui-u" id="sitemap-4">

          <p class="netscape4"><a href="#ft">Skip list</a></p>

      <ul>
            <li><a href="http://donate.fsf.org">Donate to the FSF</a></li>
        <li><a href="http://www.fsf.org/jfb">Join the FSF</a></li>

        <li><a href="/philosophy/">Free software philosophy</a><ul>
        <li><a href="/philosophy/free-sw.html">The Free Software Definition</a></li>
        <li><a href="/philosophy/pragmatic.html">Copyleft: Pragmatic Idealism</a></li>
        <li><a href="/philosophy/free-doc.html">Free Software and Free Manuals</a></li>
        <li><a href="/philosophy/selling.html">Selling Free Software</a></li>
        <li><a href="/philosophy/fs-motives.html">Motives for Writing Free Software</a></li>

        <li><a href="/philosophy/right-to-read.html">The Right To Read</a></li>
        <li><a href="/philosophy/open-source-misses-the-point.html">Why Open Source Misses the Point of Free Software</a></li></ul></li>
      <li><a href="/software/for-windows.html">Free software for Windows</a></li>
        <li><a href="http://defectivebydesign.org/">Defective by Design &mdash; Fight DRM</a></li>
  <li><a href="http://windows7sins.org/">Windows 7 Sins</a></li>
  <li><a href="http://playogg.org/">Support free media formats</a></li>
        </ul>
    </div>
    </div>
</div>

<div class="yui-g" id="mission-statement">

<p><small>The <a href="http://www.fsf.org">Free Software
Foundation</a> is the principal organizational sponsor of the <a
href="http://www.gnu.org/">GNU Operating System</a>. <strong>Our
mission is to preserve, protect and promote the freedom to use, study,
copy, modify, and redistribute computer software, and to defend the
rights of Free Software users.</strong></small></p>

<p><small><strong>Support GNU and the FSF by <a
href="http://shop.fsf.org/">buying manuals and gear</a>, <a
href="http://www.fsf.org/join">joining the FSF as an associate
member</a> or by <a href="http://donate.fsf.org/">making a
donation</a>.</strong></small></p>

<p id="backtotop"><a href="#header">back to top</a></p>




</div>


</div>



<div id="footer">

<p>
Please send FSF &amp; GNU inquiries to 
<a href="mailto:<EMAIL>"><em><EMAIL></em></a>.
There are also <a href="/contact/">other ways to contact</a> 
the FSF.
<br />
Please send broken links and other corrections or suggestions to
<a href="mailto:<EMAIL>"><em><EMAIL></em></a>.
</p>

<p>
Please see the 
<a href="/server/standards/README.translations.html">Translations
README</a> for information on coordinating and submitting
translations of this article.
</p>

<p>
Copyright notice above.</p>
<address>51 Franklin Street, Fifth Floor, Boston, MA 02110, USA</address>
<p>
Verbatim copying and distribution of this entire article is
permitted in any medium without royalty provided this notice is 
preserved.
</p>

<p>
Updated:
<!-- timestamp start -->
$Date: 2009/06/17 19:20:32 $
<!-- timestamp end -->
</p>
</div>

<div id="translations">
<h4>Translations of this page</h4>

<!-- Please keep this list alphabetical by language code. -->
<!-- Comment what the language is for each type, i.e. de is German. -->
<!-- Write the language name in its own language (Deutsch) in the text. -->
<!-- If you add a new language here, please -->
<!-- advise <EMAIL> and add it to -->
<!--  - /home/<USER>/html/server/standards/README.translations.html -->
<!--  - one of the lists under the section "Translations Underway" -->
<!--  - if there is a translation team, you also have to add an alias -->
<!--  to mail.gnu.org:/com/mailer/aliases -->
<!-- Please also check you have the language code right; see: -->
<!-- http://www.loc.gov/standards/iso639-2/php/code_list.php -->
<!-- If the 2-letter ISO 639-1 code is not available, -->
<!-- use the 3-letter ISO 639-2. -->
<!-- Please use W3C normative character entities. -->

<ul class="translations-list">
<!-- English -->
<li><a href="/licenses/lgpl-3.0.html">English</a>&nbsp;[en]</li>
</ul>
</div>
</div>
</body>
</html>
