<HTML>

<FONT SIZE="5">

Licensed under the Apache License, Version 2.0 (the "License"); Unless required by applicable law or
agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, 
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the License for the 
specific language governing permissions and limitations under the License.
<BR>
<BR>
<B>As a software reverse engineering (SRE) framework, Ghidra is designed solely to facilitate 
lawful SRE activities.  You should always ensure that any SRE activities in which you engage are 
permissible as computer software may be protected under governing law (e.g., copyright) or under an 
applicable licensing agreement.  In making Ghidra available for public use, the National Security 
Agency does not condone or encourage any improper usage of <PERSON><PERSON>dra.  Consistent with the Apache 2.0 
license under which <PERSON><PERSON><PERSON> has been made available, you are solely responsible for determining the 
appropriateness of using or redistributing Ghidra.</B>

</FONT>
</HTML>
